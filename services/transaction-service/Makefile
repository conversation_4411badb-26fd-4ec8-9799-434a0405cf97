INFO = `git describe --tags --dirty --broken --abbrev=40`
PRE = CGO_ENABLED=0 GOOS=linux GOARCH=amd64
BINS = ./bin
API = ${BINS}/api
TEST = ${BINS}/test
TEST_RUNNER = ${BINS}/test-runner

BUILD = go build -o

all: ${API} ${TEST} ${TEST_RUNNER}

go.sum: go.mod
	go mod tidy
	go mod download

.PHONY: api
api: ${API}
${API}: go.sum
	${PRE} ${BUILD} ${API} ./cmd/api/main.go

.PHONY: test
test: ${TEST}
${TEST}: go.sum
	${PRE} ${BUILD} ${TEST} ./cmd/test/main.go

.PHONY: test-runner
test-runner: ${TEST_RUNNER}
${TEST_RUNNER}: go.sum
	${PRE} ${BUILD} ${TEST_RUNNER} ./cmd/test-runner/main.go

.PHONY: test-setup
test-setup:
	docker-compose -f build/test/docker-compose.yml up -d
	@echo "Waiting for services to be ready..."
	sleep 30

.PHONY: test-run
test-run:
	go test -v ./tests/api/...

.PHONY: test-clean
test-clean:
	docker-compose -f build/test/docker-compose.yml down -v

.PHONY: test-all
test-all: test-setup test-run test-clean

.PHONY: clean
clean:
	go clean
	rm -f ${API} ${TEST} ${TEST_RUNNER}

