package main

import (
	"fmt"
	"log"
	"os"
	"strconv"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Simple cleanup utility for test accounts
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run cleanup_accounts.go <user_id>")
		fmt.Println("Example: go run cleanup_accounts.go 1")
		os.Exit(1)
	}

	userIdStr := os.Args[1]
	userId, err := strconv.Atoi(userIdStr)
	if err != nil {
		log.Fatalf("Invalid user ID: %v", err)
	}

	// Database connection
	dsn := "host=localhost user=postgres password=fafQWllRTl4nfs dbname=monexa_test_db port=5434 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Get count before cleanup
	var countBefore int64
	err = db.Raw("SELECT COUNT(*) FROM accounts WHERE owner_id = ?", userId).Scan(&countBefore).Error
	if err != nil {
		log.Fatalf("Failed to count accounts: %v", err)
	}

	fmt.Printf("Found %d accounts for user %d\n", countBefore, userId)

	if countBefore == 0 {
		fmt.Println("No accounts to clean up")
		return
	}

	// First, set all account balances to zero to allow deletion
	result := db.Exec("UPDATE accounts SET current_amount = 0 WHERE owner_id = ?", userId)
	if result.Error != nil {
		log.Fatalf("Failed to zero account balances: %v", result.Error)
	}
	fmt.Printf("Zeroed balances for %d accounts\n", result.RowsAffected)

	// Delete all accounts for the user
	result = db.Exec("DELETE FROM accounts WHERE owner_id = ?", userId)
	if result.Error != nil {
		log.Fatalf("Failed to delete accounts: %v", result.Error)
	}

	fmt.Printf("Successfully deleted %d accounts for user %d\n", result.RowsAffected, userId)

	// Verify cleanup
	var countAfter int64
	err = db.Raw("SELECT COUNT(*) FROM accounts WHERE owner_id = ?", userId).Scan(&countAfter).Error
	if err != nil {
		log.Fatalf("Failed to verify cleanup: %v", err)
	}

	fmt.Printf("Accounts remaining for user %d: %d\n", userId, countAfter)
	
	if countAfter == 0 {
		fmt.Println("✅ Cleanup completed successfully!")
	} else {
		fmt.Printf("⚠️  Warning: %d accounts still remain\n", countAfter)
	}
}
