package helpers

import (
	"fmt"
	"transaction-service/internal/dto"
	"transaction-service/internal/models"
	"transaction-service/internal/services"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// TestAccountManager manages test accounts creation, tracking, and cleanup
type TestAccountManager struct {
	accountService services.AccountServiceInterface
	db             *gorm.DB
	createdAccounts []uint // Track account IDs created during tests
}

// NewTestAccountManager creates a new test account manager
func NewTestAccountManager(accountService services.AccountServiceInterface, db *gorm.DB) *TestAccountManager {
	return &TestAccountManager{
		accountService:  accountService,
		db:             db,
		createdAccounts: make([]uint, 0),
	}
}

// AccountExistsById checks if an account exists by ID
func (tam *TestAccountManager) AccountExistsById(accountId uint) bool {
	_, err := tam.accountService.GetAccountById(accountId)
	return err == nil
}

// AccountExistsByOwnerAndCurrency checks if an account exists for owner and currency
func (tam *TestAccountManager) AccountExistsByOwnerAndCurrency(ownerId, currencyId uint) (*models.Account, bool) {
	accounts, err := tam.accountService.GetOwnAccounts(ownerId, nil)
	if err != nil {
		return nil, false
	}
	
	for _, account := range accounts {
		if account.CurrencyId == currencyId {
			return &account, true
		}
	}
	return nil, false
}

// EnsureAccountExists ensures an account exists for the given owner and currency
// If it doesn't exist, creates one and tracks it for cleanup
func (tam *TestAccountManager) EnsureAccountExists(ownerId, currencyId uint, accountName string) (*models.Account, error) {
	// Check if account already exists
	if account, exists := tam.AccountExistsByOwnerAndCurrency(ownerId, currencyId); exists {
		return account, nil
	}

	// Create new account
	account, err := tam.CreateTestAccount(ownerId, currencyId, accountName)
	if err != nil {
		return nil, fmt.Errorf("failed to create test account: %w", err)
	}

	return account, nil
}

// CreateTestAccount creates a minimal test account and tracks it for cleanup
func (tam *TestAccountManager) CreateTestAccount(ownerId, currencyId uint, name string) (*models.Account, error) {
	accountDto := &dto.MinimalAccountCreateDto{
		OwnerId:    ownerId,
		CurrencyId: currencyId,
		Name:       name,
	}

	account, err := tam.accountService.CreateMinimal(accountDto)
	if err != nil {
		return nil, err
	}

	// Track for cleanup
	tam.createdAccounts = append(tam.createdAccounts, account.ID)
	return account, nil
}

// CreateFullTestAccount creates a full test account with all fields and tracks it for cleanup
func (tam *TestAccountManager) CreateFullTestAccount(ownerId, currencyId uint, name string, assetType string, amount decimal.Decimal) (*models.Account, error) {
	accountDto := &dto.AccountCreateDto{
		OwnerId:       ownerId,
		CurrencyId:    currencyId,
		Name:          name,
		AssetType:     assetType,
		Status:        "active",
		CurrentAmount: amount,
	}

	account, err := tam.accountService.Create(accountDto)
	if err != nil {
		return nil, err
	}

	// Track for cleanup
	tam.createdAccounts = append(tam.createdAccounts, account.ID)
	return account, nil
}

// GetCreatedAccountsCount returns the number of accounts created during tests
func (tam *TestAccountManager) GetCreatedAccountsCount() int {
	return len(tam.createdAccounts)
}

// GetCreatedAccountIds returns the IDs of all accounts created during tests
func (tam *TestAccountManager) GetCreatedAccountIds() []uint {
	return tam.createdAccounts
}

// CleanupCreatedAccounts deletes all accounts created during tests
func (tam *TestAccountManager) CleanupCreatedAccounts() error {
	var errors []error

	for _, accountId := range tam.createdAccounts {
		// First, set account balance to zero to allow deletion
		err := tam.accountService.UpdateAmount(accountId, decimal.Zero)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to zero balance for account %d: %w", accountId, err))
			continue
		}

		// Delete the account
		err = tam.accountService.DeleteAccountById(accountId)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to delete account %d: %w", accountId, err))
		}
	}

	// Clear the tracking list
	tam.createdAccounts = make([]uint, 0)

	if len(errors) > 0 {
		return fmt.Errorf("cleanup errors: %v", errors)
	}

	return nil
}

// ForceCleanupAllTestAccounts removes all accounts for test user (use with caution)
func (tam *TestAccountManager) ForceCleanupAllTestAccounts(testUserId uint) error {
	accounts, err := tam.accountService.GetOwnAccounts(testUserId, nil)
	if err != nil {
		return fmt.Errorf("failed to get test user accounts: %w", err)
	}

	var errors []error
	for _, account := range accounts {
		// Set balance to zero
		err := tam.accountService.UpdateAmount(account.ID, decimal.Zero)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to zero balance for account %d: %w", account.ID, err))
			continue
		}

		// Delete account
		err = tam.accountService.DeleteAccountById(account.ID)
		if err != nil {
			errors = append(errors, fmt.Errorf("failed to delete account %d: %w", account.ID, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("force cleanup errors: %v", errors)
	}

	return nil
}

// Reset clears the tracking list without deleting accounts
func (tam *TestAccountManager) Reset() {
	tam.createdAccounts = make([]uint, 0)
}
