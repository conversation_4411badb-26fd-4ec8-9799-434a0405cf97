# Test Helpers

This directory contains test utilities for the transaction service that help with account management, database operations, and test data creation.

## Overview

The test helpers provide:
- **Account Management**: Create, track, and cleanup test accounts
- **Database Utilities**: Database operations and statistics
- **Test Data Factory**: Generate consistent test data
- **Automatic Cleanup**: Ensure tests don't leave behind test data

## Components

### TestAccountManager (`account_manager.go`)

Manages test account lifecycle with automatic tracking and cleanup.

```go
// Create account manager
accountManager := helpers.NewTestAccountManager(accountService, db)

// Check if account exists
exists := accountManager.AccountExistsById(accountId)

// Ensure account exists (creates if needed)
account, err := accountManager.EnsureAccountExists(ownerId, currencyId, "Test Account")

// Create test account
account, err := accountManager.CreateTestAccount(ownerId, currencyId, "Test Account")

// Cleanup all created accounts
err := accountManager.CleanupCreatedAccounts()
```

### DatabaseHelper (`database.go`)

Provides database utilities for testing.

```go
// Create database helper
dbHelper := helpers.NewDatabaseHelper(db)

// Get account statistics
count, err := dbHelper.GetAccountCountByOwner(ownerId)

// Clean test data
err := dbHelper.CleanTestUserData(userId)

// Execute in transaction
err := dbHelper.WithTransaction(func(tx *gorm.DB) error {
    // Database operations
    return nil
})
```

### TestDataFactory (`factory.go`)

Generates consistent test data using factory patterns.

```go
// Create factory
factory := helpers.NewTestDataFactory()

// Create account DTOs
minimalAccount := factory.CreateMinimalAccountDto(ownerId, currencyId, "Test Account")
fullAccount := factory.CreateAccountDtoWithAmount(ownerId, currencyId, "Test Account", 100.0)
zeroAccount := factory.CreateZeroBalanceAccountDto(ownerId, currencyId, "Zero Account")

// Generate test names
name := factory.CreateTestAccountName("Account", 1) // "Account 1"
uniqueName := factory.CreateUniqueTestAccountName("Account") // "Account **********"

// Get test currencies
currencies := factory.GetTestCurrencies()
```

## Usage Patterns

### Basic Test Setup

```go
func TestAccountOperations(t *testing.T) {
    // Setup test environment
    router, factory := setupTestEnvironment(t)
    
    // Setup cleanup
    defer cleanupCreatedAccounts(t, router)
    
    // Your test code here
}
```

### Account Creation with Tracking

```go
// Create account using factory
accountData := factory.CreateAccountDtoWithAmount(
    helpers.TestUserId1,
    helpers.TestCurrencyUSD,
    "Test Account",
    100.0,
)

// Make API request
body, _ := json.Marshal(accountData)
resp, response := makeRequest(t, router, "POST", "/api/v1/account/create", body)

// Track for cleanup
if accountId := extractAccountIdFromResponse(response); accountId > 0 {
    trackCreatedAccount(accountId)
}
```

### Multiple Account Creation

```go
// Create accounts for multiple currencies
currencies := factory.GetTestCurrencies()[:3]

for i, currencyId := range currencies {
    accountData := factory.CreateAccountDtoWithAmount(
        helpers.TestUserId1,
        currencyId,
        factory.CreateTestAccountName("Multi Account", i+1),
        100.0 * float64(i+1),
    )
    
    // Create and track account
    // ... API call and tracking code
}
```

## Constants

The factory provides common test constants:

```go
const (
    TestUserId1     = uint(1)
    TestUserId2     = uint(2)
    TestCurrencyUSD = uint(1)
    TestCurrencyEUR = uint(2)
    TestCurrencyGBP = uint(3)
    TestCurrencyJPY = uint(4)
    TestCurrencyRUB = uint(5)
    TestCurrencyCNY = uint(6)
)
```

## Best Practices

### 1. Always Use Cleanup

```go
defer cleanupCreatedAccounts(t, router)
```

### 2. Track Created Accounts

```go
if accountId := extractAccountIdFromResponse(response); accountId > 0 {
    trackCreatedAccount(accountId)
}
```

### 3. Use Factory for Consistent Data

```go
// Good: Using factory
accountData := factory.CreateAccountDtoWithAmount(userId, currencyId, "Test", 100.0)

// Avoid: Manual creation
accountData := dto.AccountCreateDto{
    Name: "Test Account",
    // ... manual field setting
}
```

### 4. Check Account Existence

```go
// Ensure account exists before testing
account, err := accountManager.EnsureAccountExists(ownerId, currencyId, "Test Account")
```

### 5. Use Meaningful Test Names

```go
name := factory.CreateTestAccountName("Limit Test Account", i)
```

## Integration with Existing Tests

To integrate with existing tests:

1. **Add import**: `"transaction-service/tests/helpers"`

2. **Update test setup**:
```go
router, factory := setupTestEnvironment(t)
defer cleanupCreatedAccounts(t, router)
```

3. **Replace manual data creation** with factory methods

4. **Add account tracking** after successful creation

## Error Handling

The helpers include proper error handling:

```go
// Account creation with error handling
account, err := accountManager.CreateTestAccount(ownerId, currencyId, "Test")
if err != nil {
    t.Fatalf("Failed to create test account: %v", err)
}

// Cleanup with error logging
err := accountManager.CleanupCreatedAccounts()
if err != nil {
    t.Logf("Warning: Cleanup failed: %v", err)
}
```

## Examples

See `account_test_example.go` for complete usage examples including:
- Basic account creation with factory
- Multiple account creation
- Zero balance accounts
- Cleanup demonstration

## Future Enhancements

Planned improvements:
- Transaction test helpers
- Currency test helpers
- Enhanced database seeding
- Performance test utilities
- Mock service integration
