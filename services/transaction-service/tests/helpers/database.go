package helpers

import (
	"fmt"
	"transaction-service/internal/models"
	"transaction-service/internal/services"

	"gorm.io/gorm"
)

// DatabaseHelper provides database utilities for testing
type DatabaseHelper struct {
	db *gorm.DB
}

// NewDatabaseHelper creates a new database helper
func NewDatabaseHelper(db *gorm.DB) *DatabaseHelper {
	return &DatabaseHelper{
		db: db,
	}
}

// GetDB returns the database connection
func (dh *DatabaseHelper) GetDB() *gorm.DB {
	return dh.db
}

// CleanTable removes all records from a specific table
func (dh *DatabaseHelper) CleanTable(tableName string) error {
	return dh.db.Exec(fmt.Sprintf("DELETE FROM %s", tableName)).Error
}

// CleanAccountsTable removes all accounts from the accounts table
func (dh *DatabaseHelper) CleanAccountsTable() error {
	return dh.CleanTable("accounts")
}

// CleanTransactionsTable removes all transactions from the transactions table
func (dh *DatabaseHelper) CleanTransactionsTable() error {
	return dh.CleanTable("transactions")
}

// CleanTestUserData removes all data for a specific test user
func (dh *DatabaseHelper) CleanTestUserData(userId uint) error {
	// Clean accounts
	err := dh.db.Where("owner_id = ?", userId).Delete(&models.Account{}).Error
	if err != nil {
		return fmt.Errorf("failed to clean user accounts: %w", err)
	}

	// Clean transactions (if transaction model exists)
	// Note: Add transaction cleanup here when transaction model is available
	
	return nil
}

// ResetAutoIncrement resets the auto-increment counter for a table
func (dh *DatabaseHelper) ResetAutoIncrement(tableName string, startValue int) error {
	query := fmt.Sprintf("ALTER SEQUENCE %s_id_seq RESTART WITH %d", tableName, startValue)
	return dh.db.Exec(query).Error
}

// CheckTableExists checks if a table exists in the database
func (dh *DatabaseHelper) CheckTableExists(tableName string) bool {
	var count int64
	query := `
		SELECT COUNT(*) 
		FROM information_schema.tables 
		WHERE table_schema = 'public' AND table_name = ?
	`
	dh.db.Raw(query, tableName).Scan(&count)
	return count > 0
}

// GetTableRowCount returns the number of rows in a table
func (dh *DatabaseHelper) GetTableRowCount(tableName string) (int64, error) {
	var count int64
	err := dh.db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)).Scan(&count).Error
	return count, err
}

// GetAccountCount returns the total number of accounts
func (dh *DatabaseHelper) GetAccountCount() (int64, error) {
	return dh.GetTableRowCount("accounts")
}

// GetAccountCountByOwner returns the number of accounts for a specific owner
func (dh *DatabaseHelper) GetAccountCountByOwner(ownerId uint) (int64, error) {
	var count int64
	err := dh.db.Model(&models.Account{}).Where("owner_id = ?", ownerId).Count(&count).Error
	return count, err
}

// GetAccountCountByCurrency returns the number of accounts for a specific currency
func (dh *DatabaseHelper) GetAccountCountByCurrency(currencyId uint) (int64, error) {
	var count int64
	err := dh.db.Model(&models.Account{}).Where("currency_id = ?", currencyId).Count(&count).Error
	return count, err
}

// GetAccountCountByOwnerAndCurrency returns the number of accounts for a specific owner and currency
func (dh *DatabaseHelper) GetAccountCountByOwnerAndCurrency(ownerId, currencyId uint) (int64, error) {
	var count int64
	err := dh.db.Model(&models.Account{}).
		Where("owner_id = ? AND currency_id = ?", ownerId, currencyId).
		Count(&count).Error
	return count, err
}

// BeginTransaction starts a database transaction
func (dh *DatabaseHelper) BeginTransaction() *gorm.DB {
	return dh.db.Begin()
}

// WithTransaction executes a function within a database transaction
func (dh *DatabaseHelper) WithTransaction(fn func(tx *gorm.DB) error) error {
	return dh.db.Transaction(fn)
}

// TestAccountManager creates a new test account manager with this database helper
func (dh *DatabaseHelper) TestAccountManager(accountService services.AccountServiceInterface) *TestAccountManager {
	return NewTestAccountManager(accountService, dh.db)
}
