package tests_api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"transaction-service/core/connect"
	httpClient "transaction-service/core/http"
	"transaction-service/internal/dto"
	"transaction-service/tests/helpers"
	"transaction-service/tests/mocks"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

const testToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************.P3B3DVvzio4_F0TU9mhg9Q5mjvdUcaVpuekYupytkv8"

// Test constants
const (
	testUserId             = uint(1)
	testCurrencyID         = uint(1) // USD
	maxAccountsPerCurrency = 5
)

var (
	factory        *helpers.TestDataFactory
	cleanupManager *helpers.AutoCleanupManager
)

// makeRequest is a helper function to make HTTP requests to the router
func makeRequest(t *testing.T, router *gin.Engine, method, endpoint string, body []byte) (*httptest.ResponseRecorder, map[string]interface{}) {
	w := httptest.NewRecorder()
	req, err := http.NewRequest(method, endpoint, bytes.NewBuffer(body))
	require.NoError(t, err)

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+testToken)

	router.ServeHTTP(w, req)

	var response map[string]interface{}
	if w.Body.Len() > 0 {
		err = json.NewDecoder(w.Body).Decode(&response)
		require.NoError(t, err)
	}

	return w, response
}

func setupMockClient() *mocks.MockHTTPClient {
	connect.HTTPClient = httpClient.NewSelectiveHTTPClient(&http.Client{})
	mockClient := mocks.NewMockHTTPClient()
	httpClient.SetMockClient(mockClient)
	return mockClient
}

// setupTestEnvironment initializes the test environment with helpers
func setupTestEnvironment(t *testing.T) (*gin.Engine, *helpers.TestDataFactory) {
	// Initialize test router using shared function
	router, err := InitTestRouter()
	require.NoError(t, err)

	// Initialize factory
	factory = helpers.NewTestDataFactory()

	// Initialize auto cleanup manager for test currencies
	testCurrencies := []uint{testCurrencyID, 2, 3, 4, 5, 6} // All currencies used in tests
	cleanupManager, err = helpers.NewAutoCleanupManager(testUserId, testCurrencies)
	if err != nil {
		t.Logf("Warning: Failed to initialize cleanup manager: %v", err)
		cleanupManager = nil
	}

	return router, factory
}

// autoCleanupTestAccounts automatically cleans up only test-created accounts
func autoCleanupTestAccounts(t *testing.T) {
	if cleanupManager != nil {
		count, _ := cleanupManager.GetTestCreatedAccountsCount()
		if count > 0 {
			t.Logf("Cleaning up %d test-created accounts...", count)
			err := cleanupManager.CleanupTestCreatedAccounts()
			if err != nil {
				t.Logf("Warning: Auto cleanup failed: %v", err)
			} else {
				t.Logf("Successfully cleaned up test-created accounts")
			}
		}
		cleanupManager.Close()
	}
}

// extractAccountIdFromResponse extracts account ID from creation response
func extractAccountIdFromResponse(response map[string]interface{}) uint {
	if id, ok := response["ID"]; ok {
		if idFloat, ok := id.(float64); ok {
			return uint(idFloat)
		}
	}
	return 0
}

// TestAccountCreationLimit tests the maximum 5 accounts per currency limit
func TestAccountCreationLimit(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Setup test environment
	router, factory := setupTestEnvironment(t)

	// Save original client and restore after test
	originalClient := connect.HTTPClient
	defer func() {
		connect.HTTPClient = originalClient
		// Cleanup created accounts automatically
		autoCleanupTestAccounts(t)
	}()

	// Setup mock client for admin roles
	mockClient := setupMockClient()

	// Mock the admin roles response to allow user ID 1 as owner
	adminRolesResponse := `[{"UserId":1,"Role":"owner"}]`
	mockClient.SetJSONResponse("GET", "api/v1/user/get-all-admins", adminRolesResponse)

	t.Run("CreateAccount_MaximumLimit", func(t *testing.T) {
		// Create 5 accounts successfully (should all succeed)
		for i := 1; i <= maxAccountsPerCurrency; i++ {
			t.Run(fmt.Sprintf("Account_%d_Success", i), func(t *testing.T) {
				// Use factory to create account data
				accountData := factory.CreateAccountDtoWithAmount(
					testUserId,
					testCurrencyID,
					factory.CreateTestAccountName("Limit Test Account", i),
					100.0,
				)

				body, err := json.Marshal(accountData)
				require.NoError(t, err)

				resp, response := makeRequest(t, router, "POST", "/api/v1/account/create", body)

				assert.Equal(t, http.StatusOK, resp.Code, "Account %d should be created successfully", i)
				assert.Contains(t, response, "ID", "Response should contain account ID")

				// Account will be automatically cleaned up by autoCleanupTestAccounts
			})
		}

		// Try to create 6th account (should fail)
		t.Run("Account_6_ShouldFail", func(t *testing.T) {
			// Use factory to create account data
			accountData := factory.CreateAccountDtoWithAmount(
				testUserId,
				testCurrencyID,
				"Limit Test Account 6 - Should Fail",
				100.0,
			)

			body, err := json.Marshal(accountData)
			require.NoError(t, err)

			resp, response := makeRequest(t, router, "POST", "/api/v1/account/create", body)

			assert.Equal(t, http.StatusBadRequest, resp.Code, "6th account should fail")

			expectedMessage := "Max accounts limit reached. You can create up to 5 accounts per currency."
			assert.Equal(t, expectedMessage, response["Message"], "Should return correct error message")
		})
	})

	t.Run("CreateMinimalAccount_MaximumLimit", func(t *testing.T) {
		// Test with different currency (EUR - ID: 2) to avoid conflicts
		const testCurrencyID2 = 2

		// Create 5 minimal accounts successfully
		for i := 1; i <= maxAccountsPerCurrency; i++ {
			t.Run(fmt.Sprintf("MinimalAccount_%d_Success", i), func(t *testing.T) {
				accountData := dto.MinimalAccountCreateDto{
					Name:       fmt.Sprintf("Minimal Account %d", i),
					CurrencyId: testCurrencyID2,
				}

				body, err := json.Marshal(accountData)
				require.NoError(t, err)

				resp, response := makeRequest(t, router, "POST", "/api/v1/account/create-my-account", body)

				assert.Equal(t, http.StatusOK, resp.Code, "Minimal account %d should be created successfully", i)
				assert.Contains(t, response, "ID", "Response should contain account ID")
			})
		}

		// Try to create 6th minimal account (should fail)
		t.Run("MinimalAccount_6_ShouldFail", func(t *testing.T) {
			accountData := dto.MinimalAccountCreateDto{
				Name:       "Minimal Account 6 - Should Fail",
				CurrencyId: testCurrencyID2,
			}

			body, err := json.Marshal(accountData)
			require.NoError(t, err)

			resp, response := makeRequest(t, router, "POST", "/api/v1/account/create-my-account", body)

			assert.Equal(t, http.StatusBadRequest, resp.Code, "6th minimal account should fail")

			expectedMessage := "Max accounts limit reached. You can create up to 5 accounts per currency."
			assert.Equal(t, expectedMessage, response["Message"], "Should return correct error message")
		})
	})

	t.Run("DifferentCurrencies_ShouldAllowSeparateLimits", func(t *testing.T) {
		// Test that different currencies have separate limits
		currencies := []uint{3, 4, 5} // SOM, RUB, KZT

		for _, currencyID := range currencies {
			t.Run(fmt.Sprintf("Currency_%d_FirstAccount", currencyID), func(t *testing.T) {
				accountData := dto.MinimalAccountCreateDto{
					Name:       fmt.Sprintf("Account for Currency %d", currencyID),
					CurrencyId: currencyID,
				}

				body, err := json.Marshal(accountData)
				require.NoError(t, err)

				resp, response := makeRequest(t, router, "POST", "/api/v1/account/create-my-account", body)

				assert.Equal(t, http.StatusOK, resp.Code, "First account for currency %d should succeed", currencyID)
				assert.Contains(t, response, "ID", "Response should contain account ID")
			})
		}
	})

	t.Run("MixedEndpoints_SameCurrency", func(t *testing.T) {
		// Test that both endpoints count towards the same limit for the same currency
		const testCurrencyID3 = 6 // CNY

		// Create 3 accounts via create endpoint
		for i := 1; i <= 3; i++ {
			accountData := dto.AccountCreateDto{
				Name:          fmt.Sprintf("Mixed Test Account %d", i),
				CurrencyId:    testCurrencyID3,
				AssetType:     "digital",
				Status:        "active",
				CurrentAmount: decimal.NewFromFloat(50.0),
			}

			body, err := json.Marshal(accountData)
			require.NoError(t, err)

			resp, _ := makeRequest(t, router, "POST", "/api/v1/account/create", body)
			assert.Equal(t, http.StatusOK, resp.Code, "Mixed account %d should be created successfully", i)
		}

		// Create 2 more via minimal endpoint (should reach limit)
		for i := 4; i <= 5; i++ {
			accountData := dto.MinimalAccountCreateDto{
				Name:       fmt.Sprintf("Mixed Minimal Account %d", i),
				CurrencyId: testCurrencyID3,
			}

			body, err := json.Marshal(accountData)
			require.NoError(t, err)

			resp, _ := makeRequest(t, router, "POST", "/api/v1/account/create-my-account", body)
			assert.Equal(t, http.StatusOK, resp.Code, "Mixed minimal account %d should be created successfully", i)
		}

		// Try to create 6th account via either endpoint (should fail)
		t.Run("SixthAccount_ViaBothEndpoints_ShouldFail", func(t *testing.T) {
			// Try via create endpoint
			accountData := dto.AccountCreateDto{
				Name:          "Mixed Test Account 6 - Should Fail",
				CurrencyId:    testCurrencyID3,
				AssetType:     "digital",
				Status:        "active",
				CurrentAmount: decimal.NewFromFloat(50.0),
			}

			body, err := json.Marshal(accountData)
			require.NoError(t, err)

			resp, _ := makeRequest(t, router, "POST", "/api/v1/account/create", body)
			assert.Equal(t, http.StatusBadRequest, resp.Code, "6th account via create should fail")

			// Try via minimal endpoint
			minimalData := dto.MinimalAccountCreateDto{
				Name:       "Mixed Minimal Account 6 - Should Fail",
				CurrencyId: testCurrencyID3,
			}

			body2, err := json.Marshal(minimalData)
			require.NoError(t, err)

			resp2, _ := makeRequest(t, router, "POST", "/api/v1/account/create-my-account", body2)
			assert.Equal(t, http.StatusBadRequest, resp2.Code, "6th account via minimal should fail")
		})
	})
}
