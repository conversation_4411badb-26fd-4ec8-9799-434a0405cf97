package tests_api

import (
	"encoding/json"
	"net/http"
	"os"
	"testing"
	"transaction-service/core/connect"
	httpClient "transaction-service/core/http"
	"transaction-service/tests/helpers"
	"transaction-service/tests/mocks"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAccountWithHelpers demonstrates how to use the new test helpers
func TestAccountWithHelpers(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Setup test environment
	router, factory := setupTestEnvironment(t)

	// Save original client and restore after test
	originalClient := connect.HTTPClient
	defer func() {
		connect.HTTPClient = originalClient
		// Cleanup created accounts
		cleanupCreatedAccounts(t, router)
	}()

	// Setup mock client for admin roles
	mockClient := setupMockClient()
	adminRolesResponse := `[{"UserId":1,"Role":"owner"}]`
	mockClient.SetJSONResponse("GET", "api/v1/user/get-all-admins", adminRolesResponse)

	t.Run("CreateAccount_WithFactory", func(t *testing.T) {
		// Use factory to create test account data
		accountData := factory.CreateAccountDtoWithAmount(
			helpers.TestUserId1,
			helpers.TestCurrencyUSD,
			"Factory Test Account",
			250.50,
		)

		body, err := json.Marshal(accountData)
		require.NoError(t, err)

		resp, response := makeRequest(t, router, "POST", "/api/v1/account/create", body)

		assert.Equal(t, http.StatusOK, resp.Code, "Account should be created successfully")
		assert.Contains(t, response, "ID", "Response should contain account ID")

		// Track created account for cleanup
		if accountId := extractAccountIdFromResponse(response); accountId > 0 {
			trackCreatedAccount(accountId)
		}
	})

	t.Run("CreateMinimalAccount_WithFactory", func(t *testing.T) {
		// Use factory to create minimal account data
		accountData := factory.CreateMinimalAccountDto(
			helpers.TestUserId1,
			helpers.TestCurrencyEUR,
			"Minimal Test Account",
		)

		body, err := json.Marshal(accountData)
		require.NoError(t, err)

		resp, response := makeRequest(t, router, "POST", "/api/v1/account/create-my-account", body)

		assert.Equal(t, http.StatusOK, resp.Code, "Minimal account should be created successfully")
		assert.Contains(t, response, "ID", "Response should contain account ID")

		// Track created account for cleanup
		if accountId := extractAccountIdFromResponse(response); accountId > 0 {
			trackCreatedAccount(accountId)
		}
	})

	t.Run("CreateMultipleAccounts_DifferentCurrencies", func(t *testing.T) {
		// Create accounts for multiple currencies using factory
		currencies := factory.GetTestCurrencies()[:3] // Use first 3 currencies

		for i, currencyId := range currencies {
			accountData := factory.CreateAccountDtoWithAmount(
				helpers.TestUserId1,
				currencyId,
				factory.CreateTestAccountName("Multi-Currency Account", i+1),
				100.0*float64(i+1),
			)

			body, err := json.Marshal(accountData)
			require.NoError(t, err)

			resp, response := makeRequest(t, router, "POST", "/api/v1/account/create", body)

			assert.Equal(t, http.StatusOK, resp.Code, "Account for currency %d should be created", currencyId)
			assert.Contains(t, response, "ID", "Response should contain account ID")

			// Track created account for cleanup
			if accountId := extractAccountIdFromResponse(response); accountId > 0 {
				trackCreatedAccount(accountId)
			}
		}
	})

	t.Run("CreateZeroBalanceAccount", func(t *testing.T) {
		// Create account with zero balance using factory
		accountData := factory.CreateZeroBalanceAccountDto(
			helpers.TestUserId1,
			helpers.TestCurrencyGBP,
			"Zero Balance Test Account",
		)

		body, err := json.Marshal(accountData)
		require.NoError(t, err)

		resp, response := makeRequest(t, router, "POST", "/api/v1/account/create", body)

		assert.Equal(t, http.StatusOK, resp.Code, "Zero balance account should be created")
		assert.Contains(t, response, "ID", "Response should contain account ID")

		// Track created account for cleanup
		if accountId := extractAccountIdFromResponse(response); accountId > 0 {
			trackCreatedAccount(accountId)
		}
	})
}

// TestAccountCleanupDemo demonstrates cleanup functionality
func TestAccountCleanupDemo(t *testing.T) {
	// Skip if TEST_ENV not set
	if os.Getenv("TEST_ENV") != "true" {
		t.Skip("Skipping integration test: TEST_ENV not set")
	}

	// Setup test environment
	router, factory := setupTestEnvironment(t)

	// Save original client and restore after test
	originalClient := connect.HTTPClient
	defer func() {
		connect.HTTPClient = originalClient
		// Cleanup created accounts
		cleanupCreatedAccounts(t, router)
	}()

	// Setup mock client
	mockClient := setupMockClient()
	adminRolesResponse := `[{"UserId":1,"Role":"owner"}]`
	mockClient.SetJSONResponse("GET", "api/v1/user/get-all-admins", adminRolesResponse)

	// Create some test accounts
	for i := 1; i <= 3; i++ {
		accountData := factory.CreateAccountDtoWithAmount(
			helpers.TestUserId1,
			helpers.TestCurrencyUSD,
			factory.CreateTestAccountName("Cleanup Demo Account", i),
			50.0*float64(i),
		)

		body, err := json.Marshal(accountData)
		require.NoError(t, err)

		resp, response := makeRequest(t, router, "POST", "/api/v1/account/create", body)
		require.Equal(t, http.StatusOK, resp.Code, "Account %d should be created", i)

		// Track created account for cleanup
		if accountId := extractAccountIdFromResponse(response); accountId > 0 {
			trackCreatedAccount(accountId)
			t.Logf("Created account %d for cleanup demo", accountId)
		}
	}

	// Verify accounts were tracked
	assert.Equal(t, 3, len(createdAccountIds), "Should have tracked 3 created accounts")

	// The cleanup will happen automatically in the defer function
	t.Log("Accounts will be cleaned up automatically after test completion")
}
