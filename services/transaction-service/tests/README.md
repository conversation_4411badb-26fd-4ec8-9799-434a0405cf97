# Transaction Service API Testing

This directory contains comprehensive API tests for the transaction service. The testing environment is designed to test the full REST API functionality with real database interactions.

## Overview

The testing framework provides:
- **Full API Testing**: Tests all REST endpoints without authentication
- **Database Integration**: Real database operations with test data
- **Service Integration**: Tests with RabbitMQ, Redis, and PostgreSQL
- **Mock Authentication**: Bypasses authentication for easier testing
- **Automated Setup/Cleanup**: Database reset between tests

## Test Structure

```
tests/
├── api/                           # API integration tests
│   ├── account_creation_limit_test.go # Account limit tests
│   ├── account_test_example.go    # Account test examples
│   ├── maintest_test.go          # Test setup utilities
│   └── transactions_test.go      # Transaction endpoint tests
├── helpers/                       # Test utilities
│   ├── account_manager.go        # Account lifecycle management
│   ├── database.go               # Database test helpers
│   ├── factory.go                # Test data factory
│   ├── test_setup.go             # Test environment setup
│   └── README.md                 # Helper documentation
├── mocks/                         # Mock implementations
└── README.md                     # This file
```

## Prerequisites

1. **Docker & Docker Compose**: For running test infrastructure
2. **Go 1.22+**: For running tests
3. **Make**: For using Makefile commands

## Quick Start

### 1. Setup Test Environment
```bash
make test-setup
```
This will:
- Start PostgreSQL test database
- Start RabbitMQ test instance
- Start Redis test instance
- Start transaction service in test mode

### 2. Run Tests
```bash
make test-run
```
This runs all API tests against the test environment.

### 3. Cleanup
```bash
make test-clean
```
This stops and removes all test containers.

### 4. Run Complete Test Cycle
```bash
make test-all
```
This runs setup → tests → cleanup in sequence.

## Manual Testing

### Start Test Server
```bash
# Build test binary
make test

# Start test infrastructure
docker-compose -f build/test/docker-compose.yml up -d

# Run test server
./bin/test
```

### Run Individual Tests
```bash
# Set test environment
export TEST_ENV=true

# Run specific test suite
go test -v ./tests/api/transaction_test.go
go test -v ./tests/api/account_test.go
go test -v ./tests/api/currency_test.go

# Run all tests
go test -v ./tests/api/...
```

## Test Configuration

### Test Server Configuration
The test server uses `config.test.json` with:
- **Database**: Isolated test database
- **Port**: 8081 (different from production)
- **Authentication**: Disabled for testing
- **External Services**: Real RabbitMQ and Redis

### Test Database
- **Host**: localhost:5433
- **Database**: monexa_test_db
- **Auto-reset**: Database is reset before each test
- **Seeded Data**: Basic currencies, banks, and fee structures

## Test Features

### 1. Transaction API Tests
- Exchange operations
- Transaction listing
- Fee calculations
- Send transactions
- Transaction details
- Status updates

### 2. Account API Tests
- Account creation
- Account listing
- Balance retrieval
- Account updates

### 3. Currency API Tests
- Currency management
- Exchange rate management
- CRUD operations

### 4. Database Integration
- Real database operations
- Transaction isolation
- Data consistency checks
- Automatic cleanup

## Test Helpers

The test helpers provide comprehensive utilities for testing:

### TestAccountManager
Manages account lifecycle with automatic cleanup:

```go
accountManager := helpers.NewTestAccountManager(accountService, db)

// Ensure account exists (creates if needed)
account, err := accountManager.EnsureAccountExists(ownerId, currencyId, "Test Account")

// Create test account
account, err := accountManager.CreateTestAccount(ownerId, currencyId, "Test Account")

// Cleanup all created accounts
err := accountManager.CleanupCreatedAccounts()
```

### TestDataFactory
Generates consistent test data:

```go
factory := helpers.NewTestDataFactory()

// Create account DTOs
minimalAccount := factory.CreateMinimalAccountDto(ownerId, currencyId, "Test Account")
fullAccount := factory.CreateAccountDtoWithAmount(ownerId, currencyId, "Test Account", 100.0)

// Generate test names
name := factory.CreateTestAccountName("Account", 1)
```

### DatabaseHelper
Provides database utilities:

```go
dbHelper := helpers.NewDatabaseHelper(db)

// Get statistics
count, err := dbHelper.GetAccountCountByOwner(ownerId)

// Clean test data
err := dbHelper.CleanTestUserData(userId)
```

## HTTP Test Client

The `TestClient` provides HTTP testing utilities:

```go
client := helpers.NewTestClient("http://localhost:8081/api/v1")

// Make requests
resp, _ := client.GET("/currency/get-all")
resp, _ := client.POST("/transaction/exchange", exchangeData)
resp, _ := client.PATCH("/account/update/1", updateData)
```

## Environment Variables

- `TEST_ENV=true`: Required to run integration tests
- `CONFIG_PATH`: Path to test configuration (optional)

## Troubleshooting

### Tests Skip with "TEST_ENV not set"
```bash
export TEST_ENV=true
```

### Test Server Not Ready
- Check if all services are running: `docker-compose -f build/test/docker-compose.yml ps`
- Check logs: `docker-compose -f build/test/docker-compose.yml logs`
- Increase wait time in test setup

### Database Connection Issues
- Ensure PostgreSQL container is running
- Check database credentials in `config.test.json`
- Verify port 5433 is available

### Port Conflicts
- Ensure ports 8081, 5433, 5672, 6381 are available
- Modify docker-compose.yml if needed

## Adding New Tests

### 1. Create Test Suite
```go
type NewTestSuite struct {
    suite.Suite
    client   *helpers.TestClient
    dbHelper *helpers.DatabaseHelper
    factory  *helpers.TestDataFactory
}
```

### 2. Implement Setup/Teardown
```go
func (suite *NewTestSuite) SetupSuite() {
    // Initialize test environment
}

func (suite *NewTestSuite) SetupTest() {
    // Reset database and seed data
}
```

### 3. Write Test Methods
```go
func (suite *NewTestSuite) TestNewFeature() {
    // Test implementation
}
```

### 4. Register Test Suite
```go
func TestNewTestSuite(t *testing.T) {
    suite.Run(t, new(NewTestSuite))
}
```

## Best Practices

1. **Isolation**: Each test should be independent
2. **Cleanup**: Always reset database state
3. **Assertions**: Use meaningful assertions with messages
4. **Data**: Use factory methods for test data
5. **Errors**: Always check and handle errors
6. **Documentation**: Document complex test scenarios

## Performance

- Tests run against real database for accuracy
- Database reset adds overhead but ensures isolation
- Parallel execution possible for independent tests
- Consider test data size for performance
